# Mathematics Assessment Comprehensive Database Schema Documentation

## Overview
This document provides comprehensive database schema documentation for the mathematics assessment system, designed specifically for frontend developers who need to integrate with mathematics assessment data. The schema follows the same architectural patterns as the English assessment system while supporting mathematics-specific requirements including progressive difficulty levels, interactive question types, and detailed performance analytics.

## Database Structure

### Collection Path
```
companies/{companyId}/users/{userEmail}
```

### Complete Mathematics Assessment Fields

#### Core Assessment Fields
```javascript
{
  // Basic assessment completion tracking
  mathAssessmentCompleted: boolean,           // Overall completion status
  mathCurrentLevel: string,                   // Current assessment level: "Entry", "Level1", "GCSEPart1", "GCSEPart2"
  mathOverallScore: number,                   // Combined score across all completed levels
  mathHighestLevelCompleted: string,          // Highest level successfully completed
  mathAssessmentTimestamp: timestamp,         // When assessment was last updated
  totalTimeSpentOnMath: number,              // Total time in seconds across all levels
  updatedAt: timestamp                       // Last update timestamp
}
```

#### Level-Specific Assessment Data
```javascript
{
  // Entry Level (22 questions, 30 minutes, passing: 24/44)
  mathEntryLevel: {
    completed: boolean,
    score: number,                            // 0-44 scale
    passed: boolean,                          // score >= 24
    timeSpent: number,                        // Time in seconds
    completedAt: timestamp,
    responses: Array<Object>,                 // Question responses
    topicBreakdown: {                         // Performance by topic
      arithmetic: { score: number, maxScore: number },
      fractions: { score: number, maxScore: number },
      percentages: { score: number, maxScore: number },
      basicAlgebra: { score: number, maxScore: number },
      measurement: { score: number, maxScore: number },
      dataHandling: { score: number, maxScore: number }
    }
  },

  // Level 1 (13 questions, 30 minutes, passing: 16/26)
  mathLevel1: {
    completed: boolean,
    score: number,                            // 0-26 scale
    passed: boolean,                          // score >= 16
    timeSpent: number,
    completedAt: timestamp,
    responses: Array<Object>,
    topicBreakdown: {
      advancedArithmetic: { score: number, maxScore: number },
      fractionsDecimals: { score: number, maxScore: number },
      percentagesRatio: { score: number, maxScore: number },
      algebraicExpressions: { score: number, maxScore: number },
      geometry: { score: number, maxScore: number },
      statistics: { score: number, maxScore: number }
    }
  },

  // GCSE Part 1 (7 questions, 15 minutes, passing: 5/10)
  mathGCSEPart1: {
    completed: boolean,
    score: number,                            // 0-10 scale
    passed: boolean,                          // score >= 5
    timeSpent: number,
    completedAt: timestamp,
    responses: Array<Object>,
    topicBreakdown: {
      numberOperations: { score: number, maxScore: number },
      algebraicManipulation: { score: number, maxScore: number },
      geometricReasoning: { score: number, maxScore: number },
      fractionalCalculations: { score: number, maxScore: number }
    }
  },

  // GCSE Part 2 (10 questions, 20 minutes, passing: 8/20)
  mathGCSEPart2: {
    completed: boolean,
    score: number,                            // 0-20 scale
    passed: boolean,                          // score >= 8
    timeSpent: number,
    completedAt: timestamp,
    responses: Array<Object>,
    topicBreakdown: {
      complexCalculations: { score: number, maxScore: number },
      statisticalAnalysis: { score: number, maxScore: number },
      trigonometry: { score: number, maxScore: number },
      advancedAlgebra: { score: number, maxScore: number },
      problemSolving: { score: number, maxScore: number }
    }
  }
}
```

#### Enhanced Feedback Fields (AI-Generated Analysis)
```javascript
{
  // Detailed feedback analysis (similar to English assessment structure)
  mathFeedback: {
    numericalSkills: string,                  // 50-200 characters typical
    algebraicThinking: string,                // 50-200 characters typical
    problemSolving: string,                   // 50-200 characters typical
    geometricReasoning: string,               // 50-200 characters typical
    dataHandling: string,                     // 50-200 characters typical
    overall: string                           // 100-300 characters typical
  },

  // Strengths and improvement areas
  mathStrengths: Array<string>,               // 2-5 items, 20-80 characters each
  mathImprovements: Array<string>,            // 2-5 items, 20-80 characters each

  // Course placement recommendation
  mathPlacementRecommendation: {
    recommendedLevel: string,                 // "Essentials", "Intermediate", "Advanced", "Champions"
    specificCourses: Array<string>,           // Recommended course names
    reasoning: string,                        // Justification for recommendation
    nextSteps: Array<string>                  // Suggested learning path
  }
}
```

#### Comprehensive Response Logging (Admin Transparency)
```javascript
{
  // Detailed response data for admin review and quality assurance
  mathAssessmentResponses: {
    questionResponses: Array<{
      questionId: string,
      questionText: string,
      questionType: string,                   // "multiple-choice", "numeric-input", "interactive"
      topic: string,
      level: string,
      userAnswer: any,                        // Answer value (varies by question type)
      correctAnswer: any,
      isCorrect: boolean,
      points: number,
      timeSpent: number,                      // Time on this question in seconds
      interactionData: Object,                // Interactive question interaction logs
      timestamp: timestamp
    }>,
    
    assessmentMetadata: {
      sessionId: string,
      userAgent: string,
      startTime: timestamp,
      endTime: timestamp,
      totalInteractions: number,
      calculatorUsed: boolean,
      assistiveTechnologyUsed: boolean,
      browserInfo: Object
    },
    
    interactionLog: Array<{
      action: string,                         // "question_viewed", "answer_changed", "calculator_used", etc.
      timestamp: timestamp,
      questionId: string,
      data: Object                            // Action-specific data
    }>
  }
}
```

## Field Specifications

### 1. mathFeedback (Object)
**Type**: `Object`
**Required**: Yes (with fallback defaults)
**Structure**:
```javascript
{
  numericalSkills: string,      // Assessment of arithmetic and calculation abilities
  algebraicThinking: string,    // Evaluation of algebraic reasoning and manipulation
  problemSolving: string,       // Analysis of word problem and application skills
  geometricReasoning: string,   // Assessment of spatial understanding and geometry
  dataHandling: string,         // Evaluation of statistical and data interpretation skills
  overall: string              // Comprehensive summary with next steps
}
```

**Sample Data**:
```javascript
mathFeedback: {
  numericalSkills: "Strong arithmetic foundation with accurate basic calculations. Minor issues with decimal operations but overall demonstrates solid numerical competency.",
  algebraicThinking: "Good understanding of algebraic concepts. Can solve linear equations effectively but needs practice with more complex expressions.",
  problemSolving: "Excellent at interpreting word problems and identifying relevant information. Shows logical problem-solving approach.",
  geometricReasoning: "Solid understanding of basic geometric properties. Area and perimeter calculations are accurate.",
  dataHandling: "Good interpretation of charts and graphs. Statistical concepts need further development.",
  overall: "Strong Level 1 mathematics performance. Ready for GCSE Part 1 with additional practice in algebraic manipulation and statistical analysis."
}
```

### 2. mathStrengths (Array)
**Type**: `Array<string>`
**Required**: Yes (with fallback defaults)
**Typical Length**: 2-5 items
**Item Length**: 20-80 characters each

**Sample Data**:
```javascript
mathStrengths: [
  "Accurate arithmetic calculations",
  "Strong problem-solving approach",
  "Good understanding of fractions",
  "Effective use of mathematical reasoning",
  "Clear interpretation of word problems"
]
```

### 3. mathImprovements (Array)
**Type**: `Array<string>`
**Required**: Yes (with fallback defaults)
**Typical Length**: 2-5 items
**Item Length**: 20-80 characters each

**Sample Data**:
```javascript
mathImprovements: [
  "Practice decimal operations",
  "Strengthen algebraic manipulation skills",
  "Review statistical concepts",
  "Work on complex problem-solving strategies",
  "Improve geometric calculation accuracy"
]
```

## Complete Document Examples

### High-Performing Student (GCSE Level)
```javascript
{
  // User identification
  email: "<EMAIL>",
  userType: "student",
  studentLevel: "adult-learner",
  
  // Core assessment data
  mathAssessmentCompleted: true,
  mathCurrentLevel: "GCSEPart2",
  mathOverallScore: 78,
  mathHighestLevelCompleted: "GCSEPart2",
  mathAssessmentTimestamp: "2024-01-15T14:30:00Z",
  totalTimeSpentOnMath: 5400, // 90 minutes total
  
  // Level progression data
  mathEntryLevel: {
    completed: true,
    score: 38,
    passed: true,
    timeSpent: 1650,
    completedAt: "2024-01-15T10:30:00Z",
    topicBreakdown: {
      arithmetic: { score: 7, maxScore: 8 },
      fractions: { score: 5, maxScore: 6 },
      percentages: { score: 4, maxScore: 4 },
      basicAlgebra: { score: 5, maxScore: 6 },
      measurement: { score: 4, maxScore: 4 },
      dataHandling: { score: 4, maxScore: 4 }
    }
  },
  
  mathLevel1: {
    completed: true,
    score: 22,
    passed: true,
    timeSpent: 1500,
    completedAt: "2024-01-15T12:00:00Z"
  },
  
  mathGCSEPart1: {
    completed: true,
    score: 8,
    passed: true,
    timeSpent: 750,
    completedAt: "2024-01-15T13:15:00Z"
  },
  
  mathGCSEPart2: {
    completed: true,
    score: 16,
    passed: true,
    timeSpent: 1100,
    completedAt: "2024-01-15T14:30:00Z"
  },
  
  // Enhanced feedback data
  mathFeedback: {
    numericalSkills: "Exceptional numerical accuracy across all operations. Demonstrates advanced calculation strategies and efficiency.",
    algebraicThinking: "Strong algebraic reasoning with excellent manipulation skills. Comfortable with complex expressions and equations.",
    problemSolving: "Outstanding problem-solving abilities. Systematically approaches complex problems with clear mathematical reasoning.",
    geometricReasoning: "Excellent spatial understanding and geometric calculation accuracy. Strong grasp of advanced geometric concepts.",
    dataHandling: "Advanced statistical analysis skills. Interprets complex data sets accurately and draws appropriate conclusions.",
    overall: "Exceptional GCSE-level mathematics performance. Ready for advanced mathematical studies. Consider Champions level courses."
  },
  
  mathStrengths: [
    "Advanced algebraic manipulation",
    "Excellent problem-solving strategies",
    "Strong statistical analysis skills",
    "Accurate complex calculations",
    "Clear mathematical reasoning"
  ],
  
  mathImprovements: [
    "Explore advanced trigonometric applications",
    "Practice calculus preparation topics",
    "Develop mathematical proof techniques"
  ],
  
  mathPlacementRecommendation: {
    recommendedLevel: "Champions",
    specificCourses: ["ICDL Level 3", "Advanced Mathematical Applications"],
    reasoning: "Exceptional performance across all assessment levels demonstrates readiness for advanced mathematical studies.",
    nextSteps: ["Enroll in Champions level courses", "Consider advanced mathematics pathway", "Explore specialized mathematical applications"]
  },
  
  // System fields
  updatedAt: "2024-01-15T14:35:00Z"
}
```

### Entry-Level Student (Basic Skills)
```javascript
{
  // Core assessment data
  mathAssessmentCompleted: true,
  mathCurrentLevel: "Entry",
  mathOverallScore: 18,
  mathHighestLevelCompleted: "Entry",
  mathAssessmentTimestamp: "2024-01-15T11:00:00Z",
  totalTimeSpentOnMath: 1800, // 30 minutes

  // Entry level only
  mathEntryLevel: {
    completed: true,
    score: 18,
    passed: false, // Below passing score of 24
    timeSpent: 1800,
    completedAt: "2024-01-15T11:00:00Z",
    topicBreakdown: {
      arithmetic: { score: 4, maxScore: 8 },
      fractions: { score: 2, maxScore: 6 },
      percentages: { score: 1, maxScore: 4 },
      basicAlgebra: { score: 3, maxScore: 6 },
      measurement: { score: 3, maxScore: 4 },
      dataHandling: { score: 2, maxScore: 4 }
    }
  },

  // Enhanced feedback data
  mathFeedback: {
    numericalSkills: "Basic arithmetic skills present but need strengthening. Focus on multiplication tables and division accuracy.",
    algebraicThinking: "Limited algebraic understanding. Requires foundational work with variables and simple equations.",
    problemSolving: "Shows effort in problem-solving but needs practice breaking down complex problems into steps.",
    geometricReasoning: "Basic shape recognition good. Needs work on area and perimeter calculations.",
    dataHandling: "Can read simple charts but struggles with data interpretation and basic statistics.",
    overall: "Entry-level skills identified. Recommend starting with Essentials level courses to build strong mathematical foundation."
  },

  mathStrengths: [
    "Shows mathematical curiosity",
    "Attempts all problems systematically",
    "Good basic number recognition"
  ],

  mathImprovements: [
    "Practice basic arithmetic operations",
    "Learn multiplication tables thoroughly",
    "Work on fraction fundamentals",
    "Develop problem-solving strategies",
    "Practice reading and interpreting data"
  ],

  mathPlacementRecommendation: {
    recommendedLevel: "Essentials",
    specificCourses: ["Computer Skills Beginners", "Computer Skills Beginners Plus"],
    reasoning: "Entry-level assessment indicates need for foundational mathematical skills development before progressing to higher levels.",
    nextSteps: ["Complete Essentials level courses", "Practice basic arithmetic daily", "Retake assessment after skill development"]
  }
}
```

## Data Validation Rules

### Score-Level Consistency
```javascript
// Level progression validation logic
function validateMathProgression(userData) {
  const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
  const passingScores = { Entry: 24, Level1: 16, GCSEPart1: 5, GCSEPart2: 8 };

  for (let i = 0; i < levels.length; i++) {
    const level = levels[i];
    const levelData = userData[`math${level}`];

    if (levelData?.completed) {
      // Validate passing status
      levelData.passed = levelData.score >= passingScores[level];

      // Validate progression (can't complete higher level without passing lower)
      if (i > 0 && !userData[`math${levels[i-1]}`]?.passed) {
        throw new Error(`Cannot complete ${level} without passing ${levels[i-1]}`);
      }
    }
  }
}
```

### Required Field Fallbacks
```javascript
// Fallback values if AI analysis fails
const fallbackMathFeedback = {
  numericalSkills: 'Numerical skills assessed',
  algebraicThinking: 'Algebraic thinking evaluated',
  problemSolving: 'Problem-solving approach reviewed',
  geometricReasoning: 'Geometric understanding assessed',
  dataHandling: 'Data handling skills evaluated',
  overall: 'Mathematics assessment completed successfully'
};

const fallbackMathStrengths = ['Completed the mathematics assessment'];
const fallbackMathImprovements = ['Continue practicing mathematics'];
```

## API Endpoint Mappings

### Core Assessment Endpoints
```javascript
// Start mathematics assessment
POST /api/math-assessments/start
Body: { email, level, studentLevel }
Response: { assessmentId, level, questions, timeLimit, passingScore, maxScore }

// Submit mathematics assessment
POST /api/math-assessments/:id/submit
Body: { email, answers, timeSpent, detailedResponses }
Response: { score, passed, feedback, strengths, improvements, placementRecommendation }

// Get mathematics assessment report
GET /api/math-assessments/:email/report?company=Birmingham
Response: { userData with complete mathematics assessment data }
```

### Admin Dashboard API Endpoints
```javascript
// Get mathematics assessment analytics
GET /api/admin/math-analytics?company=Birmingham
Response: {
  totalAssessments: number,
  levelDistribution: { Entry: number, Level1: number, GCSEPart1: number, GCSEPart2: number },
  averageScores: { overall: number, byLevel: Object },
  averageDuration: number,
  responseQuality: { withDetailedResponses: number, averageQuestionsCompleted: number }
}

// Get all mathematics assessment responses with filtering
GET /api/admin/math-responses?company=Birmingham&level=Entry&limit=50
Response: { data: Array<assessmentSummary>, pagination: Object }

// Get detailed mathematics assessment data for specific user
GET /api/admin/math-responses/:email?company=Birmingham
Response: { data: completeUserAssessmentData }

// Export mathematics assessment data
GET /api/admin/math-responses/export?company=Birmingham&format=csv
Response: CSV file download or JSON export
```

### Performance and Cache Management
```javascript
// Get performance metrics
GET /api/math-assessments/performance
Response: { metrics: Object, cacheEntries: Array, uptime: number }

// Clear question cache
POST /api/math-assessments/cache/clear
Body: { level?, all? }

// Warm question cache
POST /api/math-assessments/cache/warm
Body: { levels?, background? }
```

## Frontend Integration Examples

### 1. Assessment Data Retrieval
```javascript
/**
 * Fetch complete mathematics assessment data for a user
 * @param {string} userEmail - User's email address
 * @param {string} company - Company identifier (default: 'Birmingham')
 * @returns {Promise<Object>} Complete assessment data
 */
async function fetchMathAssessmentData(userEmail, company = 'Birmingham') {
  try {
    const baseUrl = window.location.protocol === 'file:'
      ? 'http://localhost:3000'
      : window.location.origin;

    const response = await fetch(`${baseUrl}/api/math-assessments/${userEmail}/report?company=${company}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch assessment data: ${response.status}`);
    }

    const data = await response.json();
    return data.data;

  } catch (error) {
    console.error('Error fetching mathematics assessment data:', error);
    throw error;
  }
}
```

### 2. Assessment Status Badge Component
```javascript
/**
 * Create assessment status badge based on mathematics assessment data
 * @param {Object} assessmentData - Mathematics assessment data
 * @returns {HTMLElement} Status badge element
 */
function createMathAssessmentStatusBadge(assessmentData) {
  const badge = document.createElement('div');
  badge.className = 'assessment-status-badge';

  if (!assessmentData.mathAssessmentCompleted) {
    badge.className += ' status-not-started';
    badge.innerHTML = `
      <span class="status-icon">⏳</span>
      <span class="status-text">Not Started</span>
    `;
    return badge;
  }

  const highestLevel = assessmentData.mathHighestLevelCompleted;
  const overallScore = assessmentData.mathOverallScore || 0;

  // Determine status based on highest completed level
  let statusClass, statusIcon, statusText;

  switch (highestLevel) {
    case 'GCSEPart2':
      statusClass = 'status-excellent';
      statusIcon = '🏆';
      statusText = `GCSE Level Complete (${overallScore} points)`;
      break;
    case 'GCSEPart1':
      statusClass = 'status-good';
      statusIcon = '⭐';
      statusText = `GCSE Part 1 Complete (${overallScore} points)`;
      break;
    case 'Level1':
      statusClass = 'status-intermediate';
      statusIcon = '📈';
      statusText = `Level 1 Complete (${overallScore} points)`;
      break;
    case 'Entry':
      statusClass = 'status-basic';
      statusIcon = '📚';
      statusText = `Entry Level Complete (${overallScore} points)`;
      break;
    default:
      statusClass = 'status-in-progress';
      statusIcon = '🔄';
      statusText = 'Assessment In Progress';
  }

  badge.className += ` ${statusClass}`;
  badge.innerHTML = `
    <span class="status-icon">${statusIcon}</span>
    <span class="status-text">${statusText}</span>
  `;

  return badge;
}
```

### 3. Assessment Results Display Component
```javascript
/**
 * Render complete mathematics assessment results
 * @param {Object} assessmentData - Assessment data from database
 * @param {HTMLElement} container - Container element to render into
 */
function renderMathAssessmentResults(assessmentData, container) {
  const {
    mathOverallScore,
    mathHighestLevelCompleted,
    mathFeedback,
    mathStrengths,
    mathImprovements,
    mathPlacementRecommendation
  } = assessmentData;

  const isAdvanced = ['GCSEPart1', 'GCSEPart2'].includes(mathHighestLevelCompleted);

  container.innerHTML = `
    <div class="math-assessment-results">
      <div class="results-header">
        <h3>Mathematics Assessment Results</h3>
        <div class="overall-score">
          <span class="score-value">${mathOverallScore || 0}</span>
          <span class="score-label">Total Points</span>
        </div>
      </div>

      <div class="level-progression">
        ${renderLevelProgression(assessmentData)}
      </div>

      <div class="feedback-section">
        <h4>Detailed Feedback</h4>
        ${renderMathFeedback(mathFeedback)}
      </div>

      <div class="strengths-improvements">
        <div class="strengths">
          <h4>Your Strengths</h4>
          <ul>
            ${(mathStrengths || []).map(strength => `<li>${strength}</li>`).join('')}
          </ul>
        </div>

        <div class="improvements">
          <h4>Areas for Improvement</h4>
          <ul>
            ${(mathImprovements || []).map(improvement => `<li>${improvement}</li>`).join('')}
          </ul>
        </div>
      </div>

      <div class="course-recommendations">
        <h4>Recommended Next Steps</h4>
        ${renderCourseRecommendations(mathPlacementRecommendation)}
      </div>
    </div>
  `;
}

/**
 * Render level progression visualization
 */
function renderLevelProgression(assessmentData) {
  const levels = [
    { key: 'mathEntryLevel', name: 'Entry Level', maxScore: 44, passingScore: 24 },
    { key: 'mathLevel1', name: 'Level 1', maxScore: 26, passingScore: 16 },
    { key: 'mathGCSEPart1', name: 'GCSE Part 1', maxScore: 10, passingScore: 5 },
    { key: 'mathGCSEPart2', name: 'GCSE Part 2', maxScore: 20, passingScore: 8 }
  ];

  return levels.map(level => {
    const levelData = assessmentData[level.key];
    if (!levelData?.completed) {
      return `
        <div class="level-item not-completed">
          <div class="level-name">${level.name}</div>
          <div class="level-status">Not Attempted</div>
        </div>
      `;
    }

    const percentage = Math.round((levelData.score / level.maxScore) * 100);
    const statusClass = levelData.passed ? 'passed' : 'not-passed';

    return `
      <div class="level-item ${statusClass}">
        <div class="level-name">${level.name}</div>
        <div class="level-score">
          <span class="score">${levelData.score}/${level.maxScore}</span>
          <span class="percentage">(${percentage}%)</span>
        </div>
        <div class="level-status">
          ${levelData.passed ? '✅ Passed' : '❌ Not Passed'}
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${percentage}%"></div>
        </div>
      </div>
    `;
  }).join('');
}
```

### 4. Admin Dashboard Integration
```javascript
/**
 * Mathematics Assessment Dashboard Component
 */
class MathAssessmentDashboard {
  constructor(containerElement, company = 'Birmingham') {
    this.container = containerElement;
    this.company = company;
    this.data = null;
  }

  async init() {
    try {
      await this.loadData();
      this.render();
      this.setupEventListeners();
    } catch (error) {
      this.renderError(error);
    }
  }

  async loadData() {
    const baseUrl = window.location.protocol === 'file:'
      ? 'http://localhost:3000'
      : window.location.origin;

    const [analyticsResponse, responsesResponse] = await Promise.all([
      fetch(`${baseUrl}/api/admin/math-analytics?company=${this.company}`),
      fetch(`${baseUrl}/api/admin/math-responses?company=${this.company}&limit=50`)
    ]);

    if (!analyticsResponse.ok || !responsesResponse.ok) {
      throw new Error('Failed to fetch mathematics assessment data');
    }

    const analytics = await analyticsResponse.json();
    const responses = await responsesResponse.json();

    this.data = {
      analytics: analytics.data,
      responses: responses.data || []
    };
  }

  render() {
    const { analytics, responses } = this.data;

    this.container.innerHTML = `
      <div class="math-dashboard">
        <div class="dashboard-header">
          <h2>Mathematics Assessment Dashboard</h2>
          <div class="summary-stats">
            <div class="stat-card">
              <div class="stat-value">${analytics.totalAssessments}</div>
              <div class="stat-label">Total Assessments</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">${analytics.averageScores?.overall || '-'}</div>
              <div class="stat-label">Average Score</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">${Math.round(analytics.averageDuration / 60)}min</div>
              <div class="stat-label">Average Duration</div>
            </div>
          </div>
        </div>

        <div class="dashboard-content">
          <div class="level-distribution-chart">
            <h3>Level Distribution</h3>
            <canvas id="math-level-chart"></canvas>
          </div>

          <div class="recent-assessments">
            <h3>Recent Assessments</h3>
            <div class="assessments-table">
              ${this.renderAssessmentsTable(responses)}
            </div>
          </div>
        </div>
      </div>
    `;

    this.renderLevelChart(analytics.levelDistribution);
  }

  renderAssessmentsTable(responses) {
    if (!responses.length) {
      return '<p>No assessments found.</p>';
    }

    return `
      <table class="assessments-table">
        <thead>
          <tr>
            <th>Student</th>
            <th>Highest Level</th>
            <th>Overall Score</th>
            <th>Completed</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${responses.map(response => `
            <tr>
              <td>${response.name || response.userEmail}</td>
              <td>
                <span class="level-badge level-${response.highestLevelCompleted?.toLowerCase()}">
                  ${response.highestLevelCompleted || 'None'}
                </span>
              </td>
              <td>${response.overallScore || 0}</td>
              <td>${new Date(response.completedAt).toLocaleDateString()}</td>
              <td>
                <button onclick="viewMathDetails('${response.userEmail}')" class="btn-view">
                  View Details
                </button>
              </td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;
  }
}
```

## Data Flow Diagrams

### Assessment Flow
```mermaid
graph TD
    A[Student Starts Assessment] --> B[Select Level: Entry/Level1/GCSE]
    B --> C[Generate Questions via AI]
    C --> D[Present Interactive Questions]
    D --> E[Collect Responses + Interactions]
    E --> F[Submit Assessment]
    F --> G[AI Analysis & Scoring]
    G --> H[Store Results in Database]
    H --> I[Generate Feedback & Recommendations]
    I --> J[Display Results to Student]

    H --> K[Admin Dashboard Updates]
    K --> L[Analytics Aggregation]
    L --> M[Performance Metrics]
```

### Database Storage Flow
```mermaid
graph LR
    A[Assessment Submission] --> B[Level-Specific Data]
    A --> C[Overall Progress Data]
    A --> D[Detailed Response Logging]
    A --> E[AI Feedback Generation]

    B --> F[mathEntryLevel/mathLevel1/etc]
    C --> G[mathOverallScore/mathCurrentLevel]
    D --> H[mathAssessmentResponses]
    E --> I[mathFeedback/mathStrengths/mathImprovements]

    F --> J[Firestore Document]
    G --> J
    H --> J
    I --> J

    J --> K[Admin Dashboard APIs]
    J --> L[Student Progress Tracking]
```

## Relationship Mappings

### Level Progression Relationships
```javascript
// Level dependency mapping
const levelDependencies = {
  'Entry': null,                    // No prerequisites
  'Level1': 'Entry',               // Must pass Entry level
  'GCSEPart1': 'Level1',          // Must pass Level1
  'GCSEPart2': 'GCSEPart1'        // Must pass GCSEPart1
};

// Course recommendation mapping based on highest completed level
const courseRecommendations = {
  'Entry': {
    passed: ['Computer Skills Beginners', 'Computer Skills Beginners Plus'],
    failed: ['Basic Numeracy Support', 'Foundation Mathematics']
  },
  'Level1': {
    passed: ['Improvers Plus', 'Everyday Life Level 1'],
    failed: ['Computer Skills Beginners Plus', 'Entry Level Review']
  },
  'GCSEPart1': {
    passed: ['ICDL Level 2', 'Computer Skills for Work Level 2'],
    failed: ['Level 1 Review', 'Intermediate Mathematics Support']
  },
  'GCSEPart2': {
    passed: ['ICDL Level 3', 'Advanced Mathematical Applications'],
    failed: ['GCSE Part 1 Review', 'Advanced Mathematics Support']
  }
};
```

### Topic-Level Relationships
```javascript
// Topic progression across levels
const topicProgression = {
  arithmetic: ['Entry', 'Level1'],
  fractions: ['Entry', 'Level1', 'GCSEPart1'],
  percentages: ['Entry', 'Level1'],
  algebra: ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'],
  geometry: ['Level1', 'GCSEPart1'],
  statistics: ['Level1', 'GCSEPart2'],
  trigonometry: ['GCSEPart2'],
  problemSolving: ['GCSEPart2']
};
```

## Query Examples for Common Dashboard Operations

### 1. Filtering and Sorting Mathematics Assessments
```javascript
// Get all assessments for a specific level
async function getAssessmentsByLevel(level, company = 'Birmingham') {
  const snapshot = await firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .where('mathAssessmentCompleted', '==', true)
    .where('mathHighestLevelCompleted', '==', level)
    .orderBy('mathAssessmentTimestamp', 'desc')
    .get();

  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
}

// Get assessments within date range
async function getAssessmentsByDateRange(startDate, endDate, company = 'Birmingham') {
  const snapshot = await firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .where('mathAssessmentCompleted', '==', true)
    .where('mathAssessmentTimestamp', '>=', startDate)
    .where('mathAssessmentTimestamp', '<=', endDate)
    .orderBy('mathAssessmentTimestamp', 'desc')
    .get();

  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
}

// Get top performers by overall score
async function getTopPerformers(limit = 10, company = 'Birmingham') {
  const snapshot = await firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .where('mathAssessmentCompleted', '==', true)
    .orderBy('mathOverallScore', 'desc')
    .limit(limit)
    .get();

  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
}
```

### 2. Aggregating Assessment Analytics
```javascript
// Calculate level distribution statistics
async function calculateLevelDistribution(company = 'Birmingham') {
  const snapshot = await firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .where('mathAssessmentCompleted', '==', true)
    .get();

  const distribution = {
    Entry: 0,
    Level1: 0,
    GCSEPart1: 0,
    GCSEPart2: 0,
    NotCompleted: 0
  };

  snapshot.forEach(doc => {
    const data = doc.data();
    const highestLevel = data.mathHighestLevelCompleted;

    if (highestLevel && distribution.hasOwnProperty(highestLevel)) {
      distribution[highestLevel]++;
    } else {
      distribution.NotCompleted++;
    }
  });

  return distribution;
}

// Calculate average scores by topic
async function calculateTopicAverages(level, company = 'Birmingham') {
  const snapshot = await firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .where('mathAssessmentCompleted', '==', true)
    .where('mathHighestLevelCompleted', '==', level)
    .get();

  const topicTotals = {};
  const topicCounts = {};

  snapshot.forEach(doc => {
    const data = doc.data();
    const levelData = data[`math${level}`];

    if (levelData?.topicBreakdown) {
      Object.entries(levelData.topicBreakdown).forEach(([topic, scores]) => {
        if (!topicTotals[topic]) {
          topicTotals[topic] = 0;
          topicCounts[topic] = 0;
        }
        topicTotals[topic] += scores.score;
        topicCounts[topic]++;
      });
    }
  });

  const averages = {};
  Object.keys(topicTotals).forEach(topic => {
    averages[topic] = topicTotals[topic] / topicCounts[topic];
  });

  return averages;
}
```

### 3. Real-time Data Listeners
```javascript
// Listen to mathematics assessment data changes
function listenToMathAssessmentData(userEmail, company, callback) {
  const userRef = firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .doc(userEmail);

  return userRef.onSnapshot((doc) => {
    if (doc.exists) {
      const data = doc.data();

      // Extract mathematics-specific data
      const mathData = {
        mathAssessmentCompleted: data.mathAssessmentCompleted,
        mathCurrentLevel: data.mathCurrentLevel,
        mathOverallScore: data.mathOverallScore,
        mathHighestLevelCompleted: data.mathHighestLevelCompleted,
        mathAssessmentTimestamp: data.mathAssessmentTimestamp,
        totalTimeSpentOnMath: data.totalTimeSpentOnMath,

        // Level-specific data
        mathEntryLevel: data.mathEntryLevel,
        mathLevel1: data.mathLevel1,
        mathGCSEPart1: data.mathGCSEPart1,
        mathGCSEPart2: data.mathGCSEPart2,

        // Feedback data
        mathFeedback: data.mathFeedback,
        mathStrengths: data.mathStrengths,
        mathImprovements: data.mathImprovements,
        mathPlacementRecommendation: data.mathPlacementRecommendation
      };

      callback(mathData, null);
    } else {
      callback(null, null);
    }
  }, (error) => {
    console.error('Error in mathematics assessment data listener:', error);
    callback(null, error);
  });
}
```

## Security Rules

### Firestore Security Rules Example
```javascript
// Allow users to read their own mathematics assessment data
match /companies/{companyId}/users/{userId} {
  allow read: if request.auth != null && request.auth.token.email == userId;
  allow write: if request.auth != null &&
               request.auth.token.email == userId &&
               validateMathAssessmentData(request.resource.data);
}

function validateMathAssessmentData(data) {
  return (
    // Validate overall score is within reasonable bounds
    (!('mathOverallScore' in data) ||
     (data.mathOverallScore is number && data.mathOverallScore >= 0 && data.mathOverallScore <= 100)) &&

    // Validate level progression logic
    validateLevelProgression(data) &&

    // Validate level-specific scores
    validateLevelScores(data) &&

    // Validate required fields are present if assessment is completed
    (!data.mathAssessmentCompleted ||
     ('mathCurrentLevel' in data && 'mathAssessmentTimestamp' in data))
  );
}

function validateLevelProgression(data) {
  // Entry level can be completed without prerequisites
  // Level1 requires Entry level to be passed
  // GCSEPart1 requires Level1 to be passed
  // GCSEPart2 requires GCSEPart1 to be passed

  return (
    (!('mathLevel1' in data) || !data.mathLevel1.completed ||
     (data.mathEntryLevel.completed && data.mathEntryLevel.passed)) &&
    (!('mathGCSEPart1' in data) || !data.mathGCSEPart1.completed ||
     (data.mathLevel1.completed && data.mathLevel1.passed)) &&
    (!('mathGCSEPart2' in data) || !data.mathGCSEPart2.completed ||
     (data.mathGCSEPart1.completed && data.mathGCSEPart1.passed))
  );
}

function validateLevelScores(data) {
  return (
    // Entry level: 0-44 points
    (!('mathEntryLevel' in data) ||
     (data.mathEntryLevel.score >= 0 && data.mathEntryLevel.score <= 44)) &&

    // Level1: 0-26 points
    (!('mathLevel1' in data) ||
     (data.mathLevel1.score >= 0 && data.mathLevel1.score <= 26)) &&

    // GCSEPart1: 0-10 points
    (!('mathGCSEPart1' in data) ||
     (data.mathGCSEPart1.score >= 0 && data.mathGCSEPart1.score <= 10)) &&

    // GCSEPart2: 0-20 points
    (!('mathGCSEPart2' in data) ||
     (data.mathGCSEPart2.score >= 0 && data.mathGCSEPart2.score <= 20))
  );
}
```

## Migration Considerations

### Existing Records
- Records created before mathematics assessment implementation will lack mathematics-specific fields
- Frontend code must handle missing mathematics assessment data gracefully
- Implement graceful degradation for users without mathematics assessment data

### Backward Compatibility
```javascript
// Safe field access pattern for mathematics assessment data
const mathData = {
  completed: userData.mathAssessmentCompleted || false,
  currentLevel: userData.mathCurrentLevel || null,
  overallScore: userData.mathOverallScore || 0,
  highestLevel: userData.mathHighestLevelCompleted || null,

  // Level-specific data with fallbacks
  entryLevel: userData.mathEntryLevel || { completed: false, score: 0, passed: false },
  level1: userData.mathLevel1 || { completed: false, score: 0, passed: false },
  gcsePart1: userData.mathGCSEPart1 || { completed: false, score: 0, passed: false },
  gcsePart2: userData.mathGCSEPart2 || { completed: false, score: 0, passed: false },

  // Feedback with fallbacks
  feedback: userData.mathFeedback || {
    numericalSkills: 'Assessment not completed',
    algebraicThinking: 'Assessment not completed',
    problemSolving: 'Assessment not completed',
    geometricReasoning: 'Assessment not completed',
    dataHandling: 'Assessment not completed',
    overall: 'Mathematics assessment not completed'
  },

  strengths: userData.mathStrengths || [],
  improvements: userData.mathImprovements || [],
  placementRecommendation: userData.mathPlacementRecommendation || {
    recommendedLevel: 'Essentials',
    specificCourses: [],
    reasoning: 'Complete mathematics assessment for personalized recommendations',
    nextSteps: ['Take mathematics assessment']
  }
};
```

### Data Migration Script Example
```javascript
// Migration script to add default mathematics assessment fields to existing users
async function migrateMathematicsAssessmentFields(company = 'Birmingham') {
  const batch = firestore.batch();

  const snapshot = await firestore
    .collection('companies')
    .doc(company)
    .collection('users')
    .where('mathAssessmentCompleted', '==', null)
    .get();

  snapshot.forEach(doc => {
    const userRef = doc.ref;
    batch.update(userRef, {
      mathAssessmentCompleted: false,
      mathCurrentLevel: null,
      mathOverallScore: 0,
      mathHighestLevelCompleted: null,
      totalTimeSpentOnMath: 0,
      updatedAt: new Date()
    });
  });

  await batch.commit();
  console.log(`Migrated ${snapshot.size} user records with mathematics assessment fields`);
}
```

## Interactive Question Type Support

### Enhanced Mathematics Assessment Features
The database schema supports interactive question types for enhanced mathematics assessment:

```javascript
// Interactive question response logging
const interactiveQuestionResponse = {
  questionId: "math_entry_001",
  questionType: "interactive-slider",
  topic: "fractions",
  level: "Entry",
  userAnswer: 0.75,
  correctAnswer: 0.75,
  isCorrect: true,
  points: 2,
  timeSpent: 45,
  interactionData: {
    sliderMoves: 8,
    finalPosition: 0.75,
    hesitationTime: 12,
    interactionSequence: [
      { action: "slider_move", value: 0.5, timestamp: "2024-01-15T10:15:30Z" },
      { action: "slider_move", value: 0.7, timestamp: "2024-01-15T10:15:35Z" },
      { action: "slider_move", value: 0.75, timestamp: "2024-01-15T10:15:42Z" },
      { action: "answer_submit", value: 0.75, timestamp: "2024-01-15T10:16:15Z" }
    ]
  },
  timestamp: "2024-01-15T10:16:15Z"
};
```

This comprehensive database schema documentation provides frontend developers with all necessary information to integrate with the mathematics assessment system, following the established patterns from the English assessment while supporting the unique requirements of mathematics education and assessment.
```
```
```
